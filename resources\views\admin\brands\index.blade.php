<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Brands Management - ERP System</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .header .actions { margin-top: 15px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .filters { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .filters select, .filters input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; }
        .table-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; color: #333; }
        tr:hover { background: #f8f9fa; }
        .actions { display: flex; gap: 5px; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .company-badge { background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Brands Management</h1>
            <div class="actions">
                <a href="{{ route('dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
                <a href="{{ route('admin.brands.create') }}" class="btn btn-primary">+ Add New Brand</a>
            </div>
        </div>

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif

        <!-- Filters -->
        <div class="filters">
            <form method="GET" style="display: flex; align-items: center; gap: 15px;">
                <div>
                    <label for="company_id">Filter by Company:</label>
                    <select name="company_id" id="company_id" onchange="this.form.submit()">
                        <option value="">All Companies</option>
                        @foreach($companies as $company)
                            <option value="{{ $company->id }}" {{ request('company_id') == $company->id ? 'selected' : '' }}>
                                {{ $company->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <input type="text" name="search" placeholder="Search brands..." value="{{ request('search') }}" 
                           style="width: 200px;" onkeyup="if(event.key==='Enter') this.form.submit()">
                </div>
                <button type="submit" class="btn btn-primary" style="padding: 8px 16px;">Search</button>
                @if(request()->hasAny(['company_id', 'search']))
                    <a href="{{ route('admin.brands.index') }}" class="btn btn-secondary" style="padding: 8px 16px;">Clear</a>
                @endif
            </form>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Brand Name</th>
                        <th>Company</th>
                        <th>Categories</th>
                        <th>Branches</th>
                        <th>Products</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($brands as $brand)
                        <tr>
                            <td>{{ $brand->id }}</td>
                            <td><strong>{{ $brand->name }}</strong></td>
                            <td>
                                <span class="company-badge">{{ $brand->company->name }}</span>
                            </td>
                            <td>{{ $brand->categories->count() }}</td>
                            <td>{{ $brand->branches->count() }}</td>
                            <td>
                                @php
                                    $productCount = $brand->categories->sum(function($category) {
                                        return $category->divisions->sum(function($division) {
                                            return $division->groups->sum(function($group) {
                                                return $group->products->count();
                                            });
                                        });
                                    });
                                @endphp
                                {{ $productCount }}
                            </td>
                            <td>{{ $brand->created_at->format('M d, Y') }}</td>
                            <td>
                                <div class="actions">
                                    <a href="{{ route('admin.brands.show', $brand) }}" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">View</a>
                                    <a href="{{ route('admin.brands.edit', $brand) }}" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                                    <form method="POST" action="{{ route('admin.brands.destroy', $brand) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this brand? This will delete all related categories and products.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="8" style="text-align: center; color: #666; padding: 40px;">
                                @if(request()->hasAny(['company_id', 'search']))
                                    No brands found matching your criteria. <a href="{{ route('admin.brands.index') }}">View all brands</a>
                                @else
                                    No brands found. <a href="{{ route('admin.brands.create') }}">Add the first brand</a>
                                @endif
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($brands->hasPages())
            <div style="margin-top: 20px; text-align: center;">
                {{ $brands->appends(request()->query())->links() }}
            </div>
        @endif

        <!-- Quick Stats -->
        <div style="margin-top: 30px; display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #007bff;">{{ $brands->total() }}</h3>
                <p style="margin: 5px 0 0 0; color: #666;">Total Brands</p>
            </div>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #28a745;">{{ $companies->count() }}</h3>
                <p style="margin: 5px 0 0 0; color: #666;">Companies</p>
            </div>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #ffc107;">
                    {{ $brands->sum(function($brand) { return $brand->categories->count(); }) }}
                </h3>
                <p style="margin: 5px 0 0 0; color: #666;">Total Categories</p>
            </div>
        </div>
    </div>
</body>
</html>
