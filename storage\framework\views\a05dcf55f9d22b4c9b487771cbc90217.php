<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo e($user->name); ?> - User Details</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .header .actions { margin-top: 15px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.9; }
        .info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .info-card { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .info-card h3 { margin: 0 0 15px 0; color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .info-row { display: flex; justify-content: space-between; margin-bottom: 10px; padding: 8px 0; border-bottom: 1px solid #eee; }
        .info-label { font-weight: bold; color: #666; }
        .info-value { color: #333; }
        .role-badge { padding: 6px 12px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .role-admin { background: #007bff; color: white; }
        .role-manager { background: #28a745; color: white; }
        .role-user { background: #6c757d; color: white; }
        .role-pos_user { background: #ffc107; color: black; }
        .company-badge { background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #495057; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo e($user->name); ?></h1>
            <div class="actions">
                <a href="<?php echo e(route('admin.users.index')); ?>" class="btn btn-secondary">← Back to Users</a>
                <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-warning">Edit User</a>
                <?php if($user->id !== 1): ?>
                    <form method="POST" action="<?php echo e(route('admin.users.destroy', $user)); ?>" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="submit" class="btn btn-danger">Delete User</button>
                    </form>
                <?php endif; ?>
            </div>
        </div>

        <?php if($user->company && $user->company->license_expiry < now()): ?>
            <div class="alert alert-warning">
                <strong>Company License Expired:</strong> This user's company license expired on <?php echo e($user->company->license_expiry->format('M d, Y')); ?>.
            </div>
        <?php elseif($user->company && $user->company->license_expiry < now()->addDays(30)): ?>
            <div class="alert alert-warning">
                <strong>License Expiring Soon:</strong> This user's company license will expire on <?php echo e($user->company->license_expiry->format('M d, Y')); ?>.
            </div>
        <?php endif; ?>

        <!-- User Information -->
        <div class="info-grid">
            <div class="info-card">
                <h3>User Details</h3>
                <div class="info-row">
                    <span class="info-label">User ID:</span>
                    <span class="info-value"><?php echo e($user->id); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Full Name:</span>
                    <span class="info-value"><?php echo e($user->name); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Email:</span>
                    <span class="info-value"><?php echo e($user->email); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Role:</span>
                    <span class="info-value">
                        <span class="role-badge role-<?php echo e($user->role); ?>">
                            <?php echo e(ucfirst(str_replace('_', ' ', $user->role))); ?>

                        </span>
                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">Created:</span>
                    <span class="info-value"><?php echo e($user->created_at->format('M d, Y')); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Last Updated:</span>
                    <span class="info-value"><?php echo e($user->updated_at->format('M d, Y')); ?></span>
                </div>
            </div>

            <div class="info-card">
                <h3>Company & Branch Information</h3>
                <div class="info-row">
                    <span class="info-label">Company:</span>
                    <span class="info-value">
                        <?php if($user->company): ?>
                            <span class="company-badge"><?php echo e($user->company->name); ?></span>
                        <?php else: ?>
                            <span style="color: #666;">No Company Assigned</span>
                        <?php endif; ?>
                    </span>
                </div>
                <?php if($user->company): ?>
                <div class="info-row">
                    <span class="info-label">License Status:</span>
                    <span class="info-value" style="color: <?php echo e($user->company->license_expiry > now() ? '#28a745' : '#dc3545'); ?>;">
                        <?php echo e($user->company->license_expiry > now() ? 'Active' : 'Expired'); ?>

                    </span>
                </div>
                <div class="info-row">
                    <span class="info-label">License Expiry:</span>
                    <span class="info-value"><?php echo e($user->company->license_expiry->format('M d, Y')); ?></span>
                </div>
                <?php endif; ?>
                <div class="info-row">
                    <span class="info-label">Branch:</span>
                    <span class="info-value">
                        <?php if($user->branch): ?>
                            <?php echo e($user->branch->name); ?>

                            <?php if($user->branch->brand): ?>
                                <small style="color: #666;">(<?php echo e($user->branch->brand->name); ?>)</small>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #666;">No Branch Assigned</span>
                        <?php endif; ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Role Permissions -->
        <div class="info-card">
            <h3>Role Permissions</h3>
            <?php
                $permissions = [
                    'admin' => [
                        'Full access to company data',
                        'Can manage brands, branches, users, and POS devices',
                        'Can view all reports and analytics',
                        'Can configure system settings'
                    ],
                    'manager' => [
                        'Can manage products and inventory',
                        'Can view sales reports',
                        'Can manage assigned brand/branch data',
                        'Limited user management'
                    ],
                    'user' => [
                        'Basic access to view data',
                        'Can perform limited operations',
                        'Can view assigned data only',
                        'Cannot modify system settings'
                    ],
                    'pos_user' => [
                        'Access to POS system for sales',
                        'Can process transactions',
                        'Basic inventory viewing',
                        'Limited to assigned branch only'
                    ]
                ];
            ?>
            
            <?php if(isset($permissions[$user->role])): ?>
                <ul style="margin: 0; padding-left: 20px;">
                    <?php $__currentLoopData = $permissions[$user->role]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li style="margin-bottom: 5px; color: #666;"><?php echo e($permission); ?></li>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            <?php else: ?>
                <p style="color: #666;">No specific permissions defined for this role.</p>
            <?php endif; ?>
        </div>

        <!-- Activity Summary -->
        <div class="info-card">
            <h3>Activity Summary</h3>
            <div class="info-row">
                <span class="info-label">Account Status:</span>
                <span class="info-value" style="color: #28a745;">Active</span>
            </div>
            <div class="info-row">
                <span class="info-label">Last Login:</span>
                <span class="info-value" style="color: #666;">Never logged in</span>
            </div>
            <div class="info-row">
                <span class="info-label">Total Sales:</span>
                <span class="info-value">0</span>
            </div>
            <div class="info-row">
                <span class="info-label">Access Level:</span>
                <span class="info-value">
                    <?php if($user->company): ?>
                        Company: <?php echo e($user->company->name); ?>

                    <?php else: ?>
                        System Level
                    <?php endif; ?>
                </span>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="info-card">
            <h3>Quick Actions</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="btn btn-primary" style="text-align: center;">
                    Edit User Details
                </a>
                <?php if($user->company): ?>
                    <a href="<?php echo e(route('admin.companies.show', $user->company)); ?>" class="btn btn-secondary" style="text-align: center;">
                        View Company
                    </a>
                <?php endif; ?>
                <?php if($user->branch): ?>
                    <a href="#" class="btn btn-secondary" style="text-align: center;">
                        View Branch
                    </a>
                <?php endif; ?>
                <a href="<?php echo e(route('admin.users.create', ['company_id' => $user->company_id])); ?>" class="btn btn-success" style="text-align: center;">
                    Add Another User
                </a>
            </div>
        </div>
    </div>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/admin/users/show.blade.php ENDPATH**/ ?>