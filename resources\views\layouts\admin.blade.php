<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'ERP System')</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            overflow-x: hidden;
        }
        
        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar.collapsed .sidebar-header p,
        .sidebar.collapsed .menu-section-title,
        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .user-name,
        .sidebar.collapsed .user-role {
            opacity: 0;
            visibility: hidden;
        }

        .sidebar.collapsed .sidebar-header h2 {
            font-size: 16px;
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 12px;
        }

        .sidebar.collapsed .user-info {
            text-align: center;
            padding: 15px 10px;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
            position: relative;
        }

        .sidebar-header h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
            transition: all 0.3s ease;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
            transition: all 0.3s ease;
        }

        .sidebar-toggle {
            position: absolute;
            top: 20px;
            right: 15px;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }
        
        .sidebar-menu {
            padding: 20px 0;
        }
        
        .menu-section {
            margin-bottom: 30px;
        }
        
        .menu-section-title {
            padding: 0 20px 10px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
            font-weight: 600;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item span {
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
            transform: translateX(5px);
        }
        
        .menu-item.active {
            background: rgba(255,255,255,0.15);
            border-left-color: #fff;
        }
        
        .menu-item i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
        }
        
        .menu-item.restricted {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .menu-item.restricted:hover {
            background: none;
            border-left-color: transparent;
            transform: none;
        }
        
        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .user-role {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 10px;
        }
        
        .logout-btn {
            width: 100%;
            padding: 8px;
            background: rgba(220, 53, 69, 0.8);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
        }
        
        .logout-btn:hover {
            background: rgba(220, 53, 69, 1);
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 70px;
        }
        
        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .content-area {
            padding: 30px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
        
        /* Additional Styles */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @yield('styles')
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ERP System</h2>
            <p>Management Dashboard</p>
            <button class="sidebar-toggle" onclick="toggleSidebarCollapse()">
                <span id="toggle-icon">‹</span>
            </button>
        </div>
        
        <div class="sidebar-menu">
            @if(Auth::user()->isMasterAdmin())
                <!-- Master Admin Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Master Management</div>
                    <a href="{{ route('admin.companies.index') }}" class="menu-item {{ request()->routeIs('admin.companies.*') ? 'active' : '' }}">
                        <i>🏢</i> <span>Companies</span>
                    </a>
                    <a href="{{ route('admin.brands.index') }}" class="menu-item {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}">
                        <i>🏷️</i> <span>Brands</span>
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="menu-item {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i>👥</i> <span>Users</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">System</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> <span>POS Devices</span>
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    <a href="/sync-logs" class="menu-item">
                        <i>🔄</i> <span>Sync Logs</span>
                    </a>
                </div>
            @else
                <!-- Company User Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Management</div>
                    @if(Auth::user()->canManageBrands())
                        <a href="{{ route('admin.brands.index') }}" class="menu-item {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}">
                            <i>🏷️</i> <span>Brands</span>
                        </a>
                    @else
                        <a href="#" class="menu-item restricted">
                            <i>🏷️</i> <span>Brands (Admin Only)</span>
                        </a>
                    @endif

                    @if(Auth::user()->canManageBranches())
                        <a href="/branches" class="menu-item">
                            <i>🏪</i> <span>Branches</span>
                        </a>
                    @else
                        <a href="#" class="menu-item restricted">
                            <i>🏪</i> <span>Branches (Admin Only)</span>
                        </a>
                    @endif

                    <a href="{{ route('admin.users.index') }}" class="menu-item {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i>👥</i> <span>Users</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Operations</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> <span>POS Devices</span>
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    <a href="/products" class="menu-item">
                        <i>📦</i> <span>Products</span>
                    </a>
                </div>
            @endif
        </div>
        
        <!-- User Info at Bottom -->
        <div class="user-info">
            <div class="user-avatar">
                {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
            </div>
            <div class="user-name">{{ Auth::user()->name }}</div>
            <div class="user-role">
                @if(Auth::user()->isMasterAdmin())
                    Master Administrator
                @else
                    {{ ucfirst(Auth::user()->role) }} - {{ Auth::user()->company->name ?? 'No Company' }}
                @endif
            </div>
            <form action="{{ route('logout') }}" method="POST" style="margin: 0;">
                @csrf
                <button type="submit" class="logout-btn">Logout</button>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <button class="mobile-toggle" onclick="toggleSidebar()">☰</button>
                <span class="page-title">@yield('page-title', 'Dashboard')</span>
            </div>
            <div class="breadcrumb">
                @yield('breadcrumb', 'Home > Dashboard')
            </div>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger">
                    <strong>Please fix the following errors:</strong>
                    <ul style="margin: 10px 0 0 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @yield('content')
        </div>
    </div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        function toggleSidebarCollapse() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('#main-content');
            const toggleIcon = document.querySelector('#toggle-icon');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Update toggle icon
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.textContent = '›';
            } else {
                toggleIcon.textContent = '‹';
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });

        // Add tooltips for collapsed sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');

            menuItems.forEach(item => {
                const span = item.querySelector('span');
                if (span) {
                    item.setAttribute('title', span.textContent);
                }
            });
        });

        @yield('scripts')
    </script>
</body>
</html>
