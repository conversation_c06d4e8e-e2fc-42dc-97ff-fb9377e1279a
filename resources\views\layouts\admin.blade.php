<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', 'ERP System')</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            overflow-x: hidden;
        }
        
        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow-y: auto;
            overflow-x: hidden;
            box-shadow: 4px 0 20px rgba(0,0,0,0.15);
            border-right: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar.collapsed {
            width: 80px;
        }

        .sidebar.collapsed .sidebar-header p,
        .sidebar.collapsed .menu-section-title,
        .sidebar.collapsed .menu-item span,
        .sidebar.collapsed .user-name,
        .sidebar.collapsed .user-role {
            opacity: 0;
            visibility: hidden;
            transform: translateX(-10px);
        }

        .sidebar.collapsed .sidebar-header h2 {
            font-size: 18px;
            transform: scale(0.8);
        }

        .sidebar.collapsed .menu-item {
            justify-content: center;
            padding: 15px;
            margin: 5px 10px;
            border-radius: 10px;
        }

        .sidebar.collapsed .menu-item:hover {
            transform: scale(1.1);
            background: rgba(255,255,255,0.15);
        }

        .sidebar.collapsed .user-info {
            text-align: center;
            padding: 20px 10px;
        }

        .sidebar.collapsed .logout-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .sidebar-header {
            padding: 25px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.15);
            text-align: center;
            position: relative;
            background: rgba(0,0,0,0.1);
        }

        .sidebar-header h2 {
            font-size: 26px;
            font-weight: 700;
            margin-bottom: 8px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            letter-spacing: 0.5px;
        }

        .sidebar-header p {
            font-size: 13px;
            opacity: 0.9;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 300;
            letter-spacing: 0.3px;
        }

        .sidebar-toggle {
            position: absolute;
            top: 25px;
            right: 20px;
            background: rgba(255,255,255,0.15);
            border: none;
            color: white;
            width: 35px;
            height: 35px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 16px;
            font-weight: bold;
            backdrop-filter: blur(10px);
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.25);
            transform: scale(1.05);
        }

        .sidebar-toggle:active {
            transform: scale(0.95);
        }
        
        .sidebar-menu {
            padding: 30px 0;
            flex: 1;
        }

        .menu-section {
            margin-bottom: 35px;
        }

        .menu-section-title {
            padding: 0 25px 15px;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            opacity: 0.6;
            font-weight: 700;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 14px 25px;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 0 25px 25px 0;
            margin: 3px 0 3px 15px;
            position: relative;
            font-weight: 500;
        }

        .menu-item span {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 14px;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.12);
            color: white;
            transform: translateX(8px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .menu-item.active {
            background: rgba(255,255,255,0.2);
            color: white;
            transform: translateX(8px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .menu-item.active::before {
            content: '';
            position: absolute;
            left: -15px;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 0 2px 2px 0;
        }

        .menu-item i {
            width: 24px;
            margin-right: 15px;
            text-align: center;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .menu-item:hover i {
            transform: scale(1.1);
        }

        .menu-item.restricted {
            opacity: 0.4;
            cursor: not-allowed;
        }

        .menu-item.restricted:hover {
            background: none;
            transform: none;
            box-shadow: none;
        }
        
        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 25px 20px;
            border-top: 1px solid rgba(255,255,255,0.15);
            background: rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .user-avatar {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 12px;
            font-size: 18px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .user-avatar:hover {
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 8px;
            text-align: center;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .user-role {
            font-size: 11px;
            opacity: 0.8;
            margin-bottom: 15px;
            text-align: center;
            font-weight: 300;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .logout-btn {
            width: 100%;
            padding: 10px;
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            border-radius: 8px;
            color: white;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .logout-btn:active {
            transform: translateY(0);
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        .main-content.expanded {
            margin-left: 80px;
        }
        
        .top-bar {
            background: white;
            padding: 20px 35px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #f1f3f4;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }
        
        .breadcrumb {
            font-size: 14px;
            color: #666;
        }
        
        .content-area {
            padding: 30px;
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
            }
        }
        
        .mobile-toggle {
            display: none;
        }
        
        /* Additional Styles */
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; transform: translateY(-1px); }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        @yield('styles')
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ERP System</h2>
            <p>Management Dashboard</p>
            <button class="sidebar-toggle" onclick="toggleSidebarCollapse()">
                <span id="toggle-icon">‹</span>
            </button>
        </div>
        
        <div class="sidebar-menu">
            @if(Auth::user()->isMasterAdmin())
                <!-- Master Admin Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Master Management</div>
                    <a href="{{ route('admin.companies.index') }}" class="menu-item {{ request()->routeIs('admin.companies.*') ? 'active' : '' }}">
                        <i>🏢</i> <span>Companies</span>
                    </a>
                    <a href="{{ route('admin.brands.index') }}" class="menu-item {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}">
                        <i>🏷️</i> <span>Brands</span>
                    </a>
                    <a href="{{ route('admin.users.index') }}" class="menu-item {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i>👥</i> <span>Users</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">System</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> <span>POS Devices</span>
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    <a href="/sync-logs" class="menu-item">
                        <i>🔄</i> <span>Sync Logs</span>
                    </a>
                </div>
            @else
                <!-- Company User Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="{{ route('dashboard') }}" class="menu-item {{ request()->routeIs('dashboard') ? 'active' : '' }}">
                        <i>📊</i> <span>Overview</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Management</div>
                    @if(Auth::user()->canManageBrands())
                        <a href="{{ route('admin.brands.index') }}" class="menu-item {{ request()->routeIs('admin.brands.*') ? 'active' : '' }}">
                            <i>🏷️</i> <span>Brands</span>
                        </a>
                    @else
                        <a href="#" class="menu-item restricted">
                            <i>🏷️</i> <span>Brands (Admin Only)</span>
                        </a>
                    @endif

                    @if(Auth::user()->canManageBranches())
                        <a href="/branches" class="menu-item">
                            <i>🏪</i> <span>Branches</span>
                        </a>
                    @else
                        <a href="#" class="menu-item restricted">
                            <i>🏪</i> <span>Branches (Admin Only)</span>
                        </a>
                    @endif

                    <a href="{{ route('admin.users.index') }}" class="menu-item {{ request()->routeIs('admin.users.*') ? 'active' : '' }}">
                        <i>👥</i> <span>Users</span>
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">Operations</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> <span>POS Devices</span>
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> <span>Sales</span>
                    </a>
                    <a href="/products" class="menu-item">
                        <i>📦</i> <span>Products</span>
                    </a>
                </div>
            @endif
        </div>
        
        <!-- User Info at Bottom -->
        <div class="user-info">
            <div class="user-avatar">
                {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
            </div>
            <div class="user-name">{{ Auth::user()->name }}</div>
            <div class="user-role">
                @if(Auth::user()->isMasterAdmin())
                    Master Administrator
                @else
                    {{ ucfirst(Auth::user()->role) }} - {{ Auth::user()->company->name ?? 'No Company' }}
                @endif
            </div>
            <form action="{{ route('logout') }}" method="POST" style="margin: 0;">
                @csrf
                <button type="submit" class="logout-btn">Logout</button>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <button class="mobile-toggle" onclick="toggleSidebar()">☰</button>
                <span class="page-title">@yield('page-title', 'Dashboard')</span>
            </div>
            <div class="breadcrumb">
                @yield('breadcrumb', 'Home > Dashboard')
            </div>
        </div>
        
        <!-- Content Area -->
        <div class="content-area">
            @if(session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif

            @if(session('error'))
                <div class="alert alert-danger">
                    {{ session('error') }}
                </div>
            @endif

            @if($errors->any())
                <div class="alert alert-danger">
                    <strong>Please fix the following errors:</strong>
                    <ul style="margin: 10px 0 0 20px;">
                        @foreach($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            @yield('content')
        </div>
    </div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        function toggleSidebarCollapse() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('#main-content');
            const toggleIcon = document.querySelector('#toggle-icon');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // Update toggle icon
            if (sidebar.classList.contains('collapsed')) {
                toggleIcon.textContent = '›';
            } else {
                toggleIcon.textContent = '‹';
            }
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });

        // Add tooltips for collapsed sidebar
        document.addEventListener('DOMContentLoaded', function() {
            const menuItems = document.querySelectorAll('.menu-item');

            menuItems.forEach(item => {
                const span = item.querySelector('span');
                if (span) {
                    item.setAttribute('title', span.textContent);
                }
            });
        });

        @yield('scripts')
    </script>
</body>
</html>
