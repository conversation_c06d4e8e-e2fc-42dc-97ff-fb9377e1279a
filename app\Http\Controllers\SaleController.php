<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Sale;

class SaleController extends Controller
{
    public function index()
    {
        return Sale::all();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'pos_device_id' => 'required|exists:pos_devices,id',
            'total_amount' => 'required|numeric|min:0',
            'sale_date' => 'required|date',
            // add other fields as necessary
        ]);

        return Sale::create($validated);
    }

    public function show(Sale $sale)
    {
        return $sale;
    }

    public function update(Request $request, Sale $sale)
    {
        $sale->update($request->all());
        return $sale;
    }

    public function destroy(Sale $sale)
    {
        $sale->delete();
        return response()->noContent();
    }
}
