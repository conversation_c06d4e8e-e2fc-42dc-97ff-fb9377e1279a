<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Division;

class DivisionController extends Controller
{
    public function index()
    {
        return Division::all();
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'category_id' => 'required|exists:categories,id',
        ]);

        return Division::create($validated);
    }

    public function show(Division $division)
    {
        return $division;
    }

    public function update(Request $request, Division $division)
    {
        $division->update($request->all());
        return $division;
    }

    public function destroy(Division $division)
    {
        $division->delete();
        return response()->noContent();
    }
}
