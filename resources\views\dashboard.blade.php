<!-- resources/views/dashboard.blade.php -->
<!DOCTYPE html>
<html>
<head>
    <title>ERP Controllers Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 30px; }
        h1 { color: #333; }
        ul { list-style: none; padding: 0; }
        li { margin: 10px 0; }
        a { text-decoration: none; color: #007bff; }
        a:hover { text-decoration: underline; }
        .section { margin-bottom: 30px; }
        .user-info {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info h3 { margin: 0 0 10px 0; color: #333; }
        .user-info p { margin: 5px 0; color: #666; }
        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .logout-btn:hover { background: #c82333; color: white; }
        .master-admin { color: #28a745; font-weight: bold; }
        .restricted { opacity: 0.6; }
        .restricted a { color: #999; pointer-events: none; }
    </style>
</head>
<body>
    <div class="user-info">
        <h3>Welcome, {{ Auth::user()->name }}!</h3>
        <p><strong>Email:</strong> {{ Auth::user()->email }}</p>
        <p><strong>Company:</strong>
            @if(Auth::user()->company)
                {{ Auth::user()->company->name }}
            @else
                <span class="master-admin">System Administrator (All Companies)</span>
            @endif
        </p>
        <p><strong>Role:</strong>
            @if(Auth::user()->isMasterAdmin())
                <span class="master-admin">Master Administrator</span>
            @else
                {{ ucfirst(Auth::user()->role) }}
            @endif
        </p>
        @if(Auth::user()->branch)
            <p><strong>Branch:</strong> {{ Auth::user()->branch->name }}</p>
        @endif
        <a href="{{ route('logout') }}" class="logout-btn"
           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
            Logout
        </a>
        <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
            @csrf
        </form>
    </div>

    @if(Auth::user()->isMasterAdmin())
        <h1>Master Admin Dashboard</h1>

        <!-- Master Admin Overview Cards -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px;">
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #007bff;">
                <h3 style="margin: 0 0 10px 0; color: #007bff;">Companies</h3>
                <p style="font-size: 24px; font-weight: bold; margin: 0;">{{ \App\Models\Company::count() }}</p>
                <p style="margin: 5px 0 0 0; color: #666;">Total Licensed Companies</p>
                <a href="{{ route('admin.companies.index') }}" style="color: #007bff; text-decoration: none; font-size: 14px;">Manage Companies →</a>
            </div>

            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #28a745;">
                <h3 style="margin: 0 0 10px 0; color: #28a745;">Brands</h3>
                <p style="font-size: 24px; font-weight: bold; margin: 0;">{{ \App\Models\Brand::count() }}</p>
                <p style="margin: 5px 0 0 0; color: #666;">Total Brands</p>
                <a href="{{ route('admin.brands.index') }}" style="color: #28a745; text-decoration: none; font-size: 14px;">Manage Brands →</a>
            </div>

            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #ffc107;">
                <h3 style="margin: 0 0 10px 0; color: #ffc107;">Users</h3>
                <p style="font-size: 24px; font-weight: bold; margin: 0;">{{ \App\Models\User::where('id', '!=', 1)->count() }}</p>
                <p style="margin: 5px 0 0 0; color: #666;">Total System Users</p>
                <a href="{{ route('admin.users.index') }}" style="color: #ffc107; text-decoration: none; font-size: 14px;">Manage Users →</a>
            </div>

            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border-left: 4px solid #dc3545;">
                <h3 style="margin: 0 0 10px 0; color: #dc3545;">POS Devices</h3>
                <p style="font-size: 24px; font-weight: bold; margin: 0;">{{ \App\Models\PosDevice::count() }}</p>
                <p style="margin: 5px 0 0 0; color: #666;">Active POS Devices</p>
                <a href="{{ route('admin.pos-devices.index') }}" style="color: #dc3545; text-decoration: none; font-size: 14px;">Manage Devices →</a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="section">
            <h2>Quick Actions</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <a href="{{ route('admin.companies.create') }}" style="background: #007bff; color: white; padding: 15px; border-radius: 5px; text-decoration: none; text-align: center; display: block;">
                    + Add New Company
                </a>
                <a href="{{ route('admin.brands.create') }}" style="background: #28a745; color: white; padding: 15px; border-radius: 5px; text-decoration: none; text-align: center; display: block;">
                    + Add New Brand
                </a>
                <a href="{{ route('admin.users.create') }}" style="background: #ffc107; color: white; padding: 15px; border-radius: 5px; text-decoration: none; text-align: center; display: block;">
                    + Add New User
                </a>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="section">
            <h2>Recent Activity</h2>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h4>Recently Added Companies</h4>
                @php
                    $recentCompanies = \App\Models\Company::latest()->take(5)->get();
                @endphp
                @if($recentCompanies->count() > 0)
                    <ul style="list-style: none; padding: 0;">
                        @foreach($recentCompanies as $company)
                            <li style="padding: 8px 0; border-bottom: 1px solid #eee;">
                                <strong>{{ $company->name }}</strong>
                                <span style="color: #666; font-size: 12px;">- {{ $company->created_at->diffForHumans() }}</span>
                                <span style="float: right; font-size: 12px; color: {{ $company->license_expiry > now() ? '#28a745' : '#dc3545' }};">
                                    License: {{ $company->license_expiry > now() ? 'Active' : 'Expired' }}
                                </span>
                            </li>
                        @endforeach
                    </ul>
                @else
                    <p style="color: #666;">No companies added yet.</p>
                @endif
            </div>
        </div>
    @else
        <h1>Company Dashboard</h1>

        <!-- Company User Dashboard -->
        <div class="section">
            <h2>Your Company: {{ Auth::user()->company->name }}</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h4>Brands</h4>
                    <p style="font-size: 20px; font-weight: bold;">{{ Auth::user()->company->brands->count() }}</p>
                </div>
                <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h4>Branches</h4>
                    <p style="font-size: 20px; font-weight: bold;">{{ Auth::user()->company->branches->count() }}</p>
                </div>
                <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <h4>POS Devices</h4>
                    <p style="font-size: 20px; font-weight: bold;">{{ Auth::user()->company->branches->sum(function($branch) { return $branch->posDevices->count(); }) }}</p>
                </div>
            </div>
        </div>
    @endif

    <div class="section">
        <h2>System Access</h2>
        <ul>
            @if(Auth::user()->canManageCompanies())
                <li><a href="{{ route('admin.companies.index') }}">Companies Management</a></li>
            @else
                <li class="restricted"><a href="#">Companies (Master Admin Only)</a></li>
            @endif

            @if(Auth::user()->canManageBrands())
                <li><a href="{{ route('admin.brands.index') }}">Brands Management</a></li>
            @else
                <li class="restricted"><a href="#">Brands (Admin Access Required)</a></li>
            @endif

            @if(Auth::user()->canManageBranches())
                <li><a href="/branches">Branches</a></li>
            @else
                <li class="restricted"><a href="#">Branches (Admin Access Required)</a></li>
            @endif

            <li><a href="{{ route('admin.users.index') }}">Users Management</a></li>
            <li><a href="/pos-devices">POS Devices</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>Product Management</h2>
        <ul>
            <li><a href="/categories">Categories</a></li>
            <li><a href="/divisions">Divisions</a></li>
            <li><a href="/groups">Groups</a></li>
            <li><a href="/products">Products</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>POS & Sales</h2>
        <ul>
            <li><a href="/sales">Sales</a></li>
            <li><a href="/sync-logs">Sync Logs</a></li>
            <li><a href="/pos-layouts">POS Layouts</a></li>
            <li><a href="/pos-limits">POS Limits</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>Operations</h2>
        <ul>
            <li><a href="/purchase-orders">Purchase Orders</a></li>
            <li><a href="/payments">Payments</a></li>
            <li><a href="/notifications">Notifications</a></li>
        </ul>
    </div>

</body>
</html>
