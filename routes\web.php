<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\POSDeviceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DivisionController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\SyncLogController;
use App\Http\Controllers\POSLayoutController;
use App\Http\Controllers\POSLimitController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\NotificationController;

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// API Routes for Authentication and POS
Route::prefix('api')->group(function () {
    Route::post('/auth/login', [AuthController::class, 'apiLogin']);
    Route::post('/auth/logout', [AuthController::class, 'apiLogout'])->middleware('auth');
    Route::get('/auth/me', [AuthController::class, 'me'])->middleware('auth');
    Route::post('/pos/activate', [AuthController::class, 'activatePOS']);
});



// Protected Routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/', function () {
        return view('dashboard');
    })->name('dashboard');


    // Master Admin Only Routes
    Route::middleware(['master.admin'])->group(function () {
        Route::apiResource('companies', CompanyController::class);
    });

    // Admin Routes (Master Admin + Company Admins)
    Route::middleware(['admin'])->group(function () {
        Route::apiResource('brands', BrandController::class);
        Route::apiResource('branches', BranchController::class);
    });

    // General Authenticated Routes
    Route::apiResource('pos-devices', POSDeviceController::class);

    // POS Device management routes
    Route::put('pos-devices/{posDevice}/activate', [POSDeviceController::class, 'activate']);
    Route::put('pos-devices/{posDevice}/deactivate', [POSDeviceController::class, 'deactivate']);

    Route::apiResource('users', UserController::class);
    Route::apiResource('categories', CategoryController::class);
    Route::apiResource('divisions', DivisionController::class);
    Route::apiResource('groups', GroupController::class);
    Route::apiResource('products', ProductController::class);

    // Additional product routes
    Route::put('products/{product}/stock', [ProductController::class, 'updateStock']);

    Route::apiResource('sales', SaleController::class);

    // POS Sync routes
    Route::post('pos/sync', [SaleController::class, 'sync']);

    Route::apiResource('sync-logs', SyncLogController::class);
    Route::apiResource('pos-layouts', POSLayoutController::class);
    Route::apiResource('pos-limits', POSLimitController::class);
    Route::apiResource('purchase-orders', PurchaseOrderController::class);
    Route::apiResource('payments', PaymentController::class);
    Route::apiResource('notifications', NotificationController::class);

    // Admin Web Routes (Master Admin Only)
    Route::prefix('admin')->name('admin.')->middleware('master.admin')->group(function () {
        // Companies Management
        Route::get('companies', [AdminController::class, 'companiesIndex'])->name('companies.index');
        Route::get('companies/create', [AdminController::class, 'companiesCreate'])->name('companies.create');
        Route::post('companies', [AdminController::class, 'companiesStore'])->name('companies.store');
        Route::get('companies/{company}', [AdminController::class, 'companiesShow'])->name('companies.show');
        Route::get('companies/{company}/edit', [AdminController::class, 'companiesEdit'])->name('companies.edit');
        Route::put('companies/{company}', [AdminController::class, 'companiesUpdate'])->name('companies.update');
        Route::delete('companies/{company}', [AdminController::class, 'companiesDestroy'])->name('companies.destroy');

        // Brands Management
        Route::get('brands', [AdminController::class, 'brandsIndex'])->name('brands.index');
        Route::get('brands/create', [AdminController::class, 'brandsCreate'])->name('brands.create');
        Route::post('brands', [AdminController::class, 'brandsStore'])->name('brands.store');
        Route::get('brands/{brand}', [AdminController::class, 'brandsShow'])->name('brands.show');
        Route::get('brands/{brand}/edit', [AdminController::class, 'brandsEdit'])->name('brands.edit');
        Route::put('brands/{brand}', [AdminController::class, 'brandsUpdate'])->name('brands.update');
        Route::delete('brands/{brand}', [AdminController::class, 'brandsDestroy'])->name('brands.destroy');

        // Users Management
        Route::get('users', [AdminController::class, 'usersIndex'])->name('users.index');
        Route::get('users/create', [AdminController::class, 'usersCreate'])->name('users.create');
        Route::post('users', [AdminController::class, 'usersStore'])->name('users.store');
        Route::get('users/{user}', [AdminController::class, 'usersShow'])->name('users.show');
        Route::get('users/{user}/edit', [AdminController::class, 'usersEdit'])->name('users.edit');
        Route::put('users/{user}', [AdminController::class, 'usersUpdate'])->name('users.update');
        Route::delete('users/{user}', [AdminController::class, 'usersDestroy'])->name('users.destroy');

        // POS Devices Management
        Route::get('pos-devices', [AdminController::class, 'posDevicesIndex'])->name('pos-devices.index');
    });
});
