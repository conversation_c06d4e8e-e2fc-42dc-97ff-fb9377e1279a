<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\CompanyController;
use App\Http\Controllers\BrandController;
use App\Http\Controllers\BranchController;
use App\Http\Controllers\POSDeviceController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DivisionController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\ProductController;
use App\Http\Controllers\SaleController;
use App\Http\Controllers\SyncLogController;
use App\Http\Controllers\POSLayoutController;
use App\Http\Controllers\POSLimitController;
use App\Http\Controllers\PurchaseOrderController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\NotificationController;

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// API route for companies list (for login form)
Route::get('/api/companies-list', [AuthController::class, 'getCompanies']);

// Protected Routes
Route::middleware(['auth'])->group(function () {
    // Dashboard
    Route::get('/', function () {
        return view('dashboard');
    })->name('dashboard');


    // Master Admin Only Routes
    Route::middleware(['master.admin'])->group(function () {
        Route::apiResource('companies', CompanyController::class);
    });

    // Admin Routes (Master Admin + Company Admins)
    Route::middleware(['admin'])->group(function () {
        Route::apiResource('brands', BrandController::class);
        Route::apiResource('branches', BranchController::class);
    });

    // General Authenticated Routes
    Route::apiResource('pos-devices', POSDeviceController::class);
    Route::apiResource('users', UserController::class);
    Route::apiResource('categories', CategoryController::class);
    Route::apiResource('divisions', DivisionController::class);
    Route::apiResource('groups', GroupController::class);
    Route::apiResource('products', ProductController::class);
    Route::apiResource('sales', SaleController::class);
    Route::apiResource('sync-logs', SyncLogController::class);
    Route::apiResource('pos-layouts', POSLayoutController::class);
    Route::apiResource('pos-limits', POSLimitController::class);
    Route::apiResource('purchase-orders', PurchaseOrderController::class);
    Route::apiResource('payments', PaymentController::class);
    Route::apiResource('notifications', NotificationController::class);
});
