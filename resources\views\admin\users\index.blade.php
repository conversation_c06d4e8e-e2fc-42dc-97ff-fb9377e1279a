<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Users Management - ERP System</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .header .actions { margin-top: 15px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .filters { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .filters select, .filters input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px; }
        .table-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; color: #333; }
        tr:hover { background: #f8f9fa; }
        .actions { display: flex; gap: 5px; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .role-badge { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
        .role-admin { background: #007bff; color: white; }
        .role-manager { background: #28a745; color: white; }
        .role-user { background: #6c757d; color: white; }
        .role-pos_user { background: #ffc107; color: black; }
        .company-badge { background: #e9ecef; padding: 4px 8px; border-radius: 4px; font-size: 12px; color: #495057; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Users Management</h1>
            <div class="actions">
                <a href="{{ route('dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
                <a href="{{ route('admin.users.create') }}" class="btn btn-primary">+ Add New User</a>
            </div>
        </div>

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif

        <!-- Filters -->
        <div class="filters">
            <form method="GET" style="display: flex; align-items: center; gap: 15px; flex-wrap: wrap;">
                <div>
                    <label for="company_id">Filter by Company:</label>
                    <select name="company_id" id="company_id" onchange="this.form.submit()">
                        <option value="">All Companies</option>
                        @foreach($companies as $company)
                            <option value="{{ $company->id }}" {{ request('company_id') == $company->id ? 'selected' : '' }}>
                                {{ $company->name }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div>
                    <label for="role">Filter by Role:</label>
                    <select name="role" id="role" onchange="this.form.submit()">
                        <option value="">All Roles</option>
                        <option value="admin" {{ request('role') == 'admin' ? 'selected' : '' }}>Admin</option>
                        <option value="manager" {{ request('role') == 'manager' ? 'selected' : '' }}>Manager</option>
                        <option value="user" {{ request('role') == 'user' ? 'selected' : '' }}>User</option>
                        <option value="pos_user" {{ request('role') == 'pos_user' ? 'selected' : '' }}>POS User</option>
                    </select>
                </div>
                <div>
                    <input type="text" name="search" placeholder="Search users..." value="{{ request('search') }}" 
                           style="width: 200px;" onkeyup="if(event.key==='Enter') this.form.submit()">
                </div>
                <button type="submit" class="btn btn-primary" style="padding: 8px 16px;">Search</button>
                @if(request()->hasAny(['company_id', 'role', 'search']))
                    <a href="{{ route('admin.users.index') }}" class="btn btn-secondary" style="padding: 8px 16px;">Clear</a>
                @endif
            </form>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Company</th>
                        <th>Branch</th>
                        <th>Created</th>
                        <th>Last Login</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($users as $user)
                        <tr>
                            <td>{{ $user->id }}</td>
                            <td><strong>{{ $user->name }}</strong></td>
                            <td>{{ $user->email }}</td>
                            <td>
                                <span class="role-badge role-{{ $user->role }}">
                                    {{ ucfirst(str_replace('_', ' ', $user->role)) }}
                                </span>
                            </td>
                            <td>
                                @if($user->company)
                                    <span class="company-badge">{{ $user->company->name }}</span>
                                @else
                                    <span style="color: #666;">No Company</span>
                                @endif
                            </td>
                            <td>
                                @if($user->branch)
                                    {{ $user->branch->name }}
                                @else
                                    <span style="color: #666;">Not assigned</span>
                                @endif
                            </td>
                            <td>{{ $user->created_at->format('M d, Y') }}</td>
                            <td>
                                <span style="color: #666;">Never</span>
                            </td>
                            <td>
                                <div class="actions">
                                    <a href="{{ route('admin.users.show', $user) }}" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">View</a>
                                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                                    @if($user->id !== 1)
                                        <form method="POST" action="{{ route('admin.users.destroy', $user) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this user?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                        </form>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="9" style="text-align: center; color: #666; padding: 40px;">
                                @if(request()->hasAny(['company_id', 'role', 'search']))
                                    No users found matching your criteria. <a href="{{ route('admin.users.index') }}">View all users</a>
                                @else
                                    No users found. <a href="{{ route('admin.users.create') }}">Add the first user</a>
                                @endif
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($users->hasPages())
            <div style="margin-top: 20px; text-align: center;">
                {{ $users->appends(request()->query())->links() }}
            </div>
        @endif

        <!-- Quick Stats -->
        <div style="margin-top: 30px; display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 20px;">
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #007bff;">{{ $users->total() }}</h3>
                <p style="margin: 5px 0 0 0; color: #666;">Total Users</p>
            </div>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #28a745;">
                    {{ \App\Models\User::where('role', 'admin')->where('id', '!=', 1)->count() }}
                </h3>
                <p style="margin: 5px 0 0 0; color: #666;">Admins</p>
            </div>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #ffc107;">
                    {{ \App\Models\User::where('role', 'pos_user')->count() }}
                </h3>
                <p style="margin: 5px 0 0 0; color: #666;">POS Users</p>
            </div>
            <div style="background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center;">
                <h3 style="margin: 0; color: #dc3545;">
                    {{ \App\Models\User::whereHas('company', function($q) { $q->where('license_expiry', '<', now()); })->count() }}
                </h3>
                <p style="margin: 5px 0 0 0; color: #666;">Expired License</p>
            </div>
        </div>
    </div>
</body>
</html>
