<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\Company;
use App\Models\Brand;
use App\Models\Branch;
use App\Models\User;

class AuthSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Master Company (for master admin)
        $masterCompany = Company::create([
            'name' => 'Master Administration',
            'license_key' => 'MASTER-ADMIN-LICENSE',
            'license_expiry' => now()->addYears(100), // Never expires
            'pos_limit' => 999
        ]);

        // Create Master Admin User (ID will be 1)
        $masterAdmin = User::create([
            'company_id' => $masterCompany->id,
            'branch_id' => null,
            'role' => 'master_admin',
            'name' => 'Master Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123')
        ]);

        // Create Sample Company
        $sampleCompany = Company::create([
            'name' => 'Sample Retail Store',
            'license_key' => 'SAMPLE-LICENSE-2024',
            'license_expiry' => now()->addYear(),
            'pos_limit' => 5
        ]);

        // Create Sample Brand
        $sampleBrand = Brand::create([
            'company_id' => $sampleCompany->id,
            'name' => 'Sample Brand'
        ]);

        // Create Sample Branch
        $sampleBranch = Branch::create([
            'company_id' => $sampleCompany->id,
            'brand_id' => $sampleBrand->id,
            'name' => 'Main Branch'
        ]);

        // Create Sample Admin User
        User::create([
            'company_id' => $sampleCompany->id,
            'branch_id' => $sampleBranch->id,
            'role' => 'admin',
            'name' => 'Company Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('admin123')
        ]);

        // Create Sample Regular User
        User::create([
            'company_id' => $sampleCompany->id,
            'branch_id' => $sampleBranch->id,
            'role' => 'user',
            'name' => 'Store Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('manager123')
        ]);

        // Create Another Sample Company
        $company2 = Company::create([
            'name' => 'Tech Solutions Ltd',
            'license_key' => 'TECH-LICENSE-2024',
            'license_expiry' => now()->addMonths(6),
            'pos_limit' => 3
        ]);

        // Create Brand for second company
        $brand2 = Brand::create([
            'company_id' => $company2->id,
            'name' => 'Tech Brand'
        ]);

        // Create Branch for second company
        $branch2 = Branch::create([
            'company_id' => $company2->id,
            'brand_id' => $brand2->id,
            'name' => 'Tech Branch'
        ]);

        // Create Admin for second company
        User::create([
            'company_id' => $company2->id,
            'branch_id' => $branch2->id,
            'role' => 'admin',
            'name' => 'Tech Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('tech123')
        ]);

        $this->command->info('Authentication seeder completed successfully!');
        $this->command->info('Master Admin: <EMAIL> / admin123');
        $this->command->info('Sample Admin: <EMAIL> / admin123');
        $this->command->info('Store Manager: <EMAIL> / manager123');
        $this->command->info('Tech Admin: <EMAIL> / tech123');
    }
}
