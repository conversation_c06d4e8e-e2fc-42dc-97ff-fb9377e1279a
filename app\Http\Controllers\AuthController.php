<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Company;

class AuthController extends Controller
{
    /**
     * Show the login form
     */
    public function showLoginForm()
    {
        if (Auth::check()) {
            return redirect()->route('dashboard');
        }
        
        return view('auth.login');
    }

    /**
     * Handle login request
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required',
            'company_id' => 'required|exists:companies,id'
        ]);

        // Find user with matching email and company_id
        $user = User::where('email', $request->email)
                   ->where('company_id', $request->company_id)
                   ->first();

        if ($user && Hash::check($request->password, $user->password)) {
            // Check if company license is valid (except for master admin)
            if (!$user->isMasterAdmin()) {
                $company = Company::find($request->company_id);
                if ($company->license_expiry < now()) {
                    return back()->withErrors([
                        'login' => 'Company license has expired. Please contact administrator.'
                    ]);
                }
            }

            Auth::login($user, $request->filled('remember'));
            
            return redirect()->intended(route('dashboard'));
        }

        return back()->withErrors([
            'login' => 'Invalid credentials or company access denied.'
        ])->withInput($request->except('password'));
    }

    /**
     * Handle logout request
     */
    public function logout(Request $request)
    {
        Auth::logout();
        
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('login');
    }


}
