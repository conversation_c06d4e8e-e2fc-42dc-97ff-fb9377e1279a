<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Companies Management - ERP System</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .header .actions { margin-top: 15px; }
        .btn { padding: 10px 20px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .table-container { background: #fff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); overflow: hidden; }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #eee; }
        th { background: #f8f9fa; font-weight: bold; color: #333; }
        tr:hover { background: #f8f9fa; }
        .status-active { color: #28a745; font-weight: bold; }
        .status-expired { color: #dc3545; font-weight: bold; }
        .actions { display: flex; gap: 5px; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .search-box { margin-bottom: 20px; }
        .search-box input { padding: 10px; border: 1px solid #ddd; border-radius: 5px; width: 300px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Companies Management</h1>
            <div class="actions">
                <a href="{{ route('dashboard') }}" class="btn btn-secondary">← Back to Dashboard</a>
                <a href="{{ route('admin.companies.create') }}" class="btn btn-primary">+ Add New Company</a>
            </div>
        </div>

        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">
                {{ session('error') }}
            </div>
        @endif

        <div class="search-box">
            <input type="text" id="searchInput" placeholder="Search companies..." onkeyup="searchTable()">
        </div>

        <div class="table-container">
            <table id="companiesTable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Company Name</th>
                        <th>License Key</th>
                        <th>License Status</th>
                        <th>License Expiry</th>
                        <th>POS Limit</th>
                        <th>Active POS</th>
                        <th>Brands</th>
                        <th>Users</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($companies as $company)
                        <tr>
                            <td>{{ $company->id }}</td>
                            <td><strong>{{ $company->name }}</strong></td>
                            <td><code>{{ $company->license_key }}</code></td>
                            <td>
                                @if($company->license_expiry > now())
                                    <span class="status-active">Active</span>
                                @else
                                    <span class="status-expired">Expired</span>
                                @endif
                            </td>
                            <td>{{ $company->license_expiry->format('M d, Y') }}</td>
                            <td>{{ $company->pos_limit }}</td>
                            <td>
                                @php
                                    $activePOS = $company->branches->sum(function($branch) { 
                                        return $branch->posDevices->where('status', 'active')->count(); 
                                    });
                                @endphp
                                {{ $activePOS }}
                            </td>
                            <td>{{ $company->brands->count() }}</td>
                            <td>{{ $company->users->count() }}</td>
                            <td>
                                <div class="actions">
                                    <a href="{{ route('admin.companies.show', $company) }}" class="btn btn-primary" style="padding: 5px 10px; font-size: 12px;">View</a>
                                    <a href="{{ route('admin.companies.edit', $company) }}" class="btn btn-warning" style="padding: 5px 10px; font-size: 12px;">Edit</a>
                                    <form method="POST" action="{{ route('admin.companies.destroy', $company) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this company? This will delete all related data.')">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;">Delete</button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="10" style="text-align: center; color: #666; padding: 40px;">
                                No companies found. <a href="{{ route('admin.companies.create') }}">Add the first company</a>
                            </td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        @if($companies->hasPages())
            <div style="margin-top: 20px; text-align: center;">
                {{ $companies->links() }}
            </div>
        @endif
    </div>

    <script>
        function searchTable() {
            const input = document.getElementById('searchInput');
            const filter = input.value.toLowerCase();
            const table = document.getElementById('companiesTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const cells = rows[i].getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length - 1; j++) {
                    if (cells[j].textContent.toLowerCase().includes(filter)) {
                        found = true;
                        break;
                    }
                }
                
                rows[i].style.display = found ? '' : 'none';
            }
        }
    </script>
</body>
</html>
