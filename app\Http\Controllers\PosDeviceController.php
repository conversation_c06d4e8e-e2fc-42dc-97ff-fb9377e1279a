<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\POSDevice;
class POSDeviceController extends Controller {
    public function index() { return POSDevice::all(); }
    public function store(Request $r) { return POSDevice::create($r->validate(['device_name'=>'required','branch_id'=>'required|exists:branches,id'])); }
    public function show(POSDevice $posDevice) { return $posDevice; }
    public function update(Request $r, POSDevice $posDevice) { $posDevice->update($r->all()); return $posDevice; }
    public function destroy(POSDevice $posDevice) { $posDevice->delete(); return response()->noContent(); }
}
