<!-- resources/views/dashboard.blade.php -->
<!DOCTYPE html>
<html>
<head>
    <title>ERP Controllers Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 30px; }
        h1 { color: #333; }
        ul { list-style: none; padding: 0; }
        li { margin: 10px 0; }
        a { text-decoration: none; color: #007bff; }
        a:hover { text-decoration: underline; }
        .section { margin-bottom: 30px; }
        .user-info {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info h3 { margin: 0 0 10px 0; color: #333; }
        .user-info p { margin: 5px 0; color: #666; }
        .logout-btn {
            background: #dc3545;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .logout-btn:hover { background: #c82333; color: white; }
        .master-admin { color: #28a745; font-weight: bold; }
        .restricted { opacity: 0.6; }
        .restricted a { color: #999; pointer-events: none; }
    </style>
</head>
<body>
    <div class="user-info">
        <h3>Welcome, <?php echo e(Auth::user()->name); ?>!</h3>
        <p><strong>Email:</strong> <?php echo e(Auth::user()->email); ?></p>
        <p><strong>Company:</strong>
            <?php if(Auth::user()->company): ?>
                <?php echo e(Auth::user()->company->name); ?>

            <?php else: ?>
                <span class="master-admin">System Administrator (All Companies)</span>
            <?php endif; ?>
        </p>
        <p><strong>Role:</strong>
            <?php if(Auth::user()->isMasterAdmin()): ?>
                <span class="master-admin">Master Administrator</span>
            <?php else: ?>
                <?php echo e(ucfirst(Auth::user()->role)); ?>

            <?php endif; ?>
        </p>
        <?php if(Auth::user()->branch): ?>
            <p><strong>Branch:</strong> <?php echo e(Auth::user()->branch->name); ?></p>
        <?php endif; ?>
        <a href="<?php echo e(route('logout')); ?>" class="logout-btn"
           onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
            Logout
        </a>
        <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" style="display: none;">
            <?php echo csrf_field(); ?>
        </form>
    </div>

    <h1>Retail ERP + POS – API Controllers</h1>

    <div class="section">
        <h2>Master Data</h2>
        <ul>
            <?php if(Auth::user()->canManageCompanies()): ?>
                <li><a href="/companies">Companies</a></li>
            <?php else: ?>
                <li class="restricted"><a href="#">Companies (Master Admin Only)</a></li>
            <?php endif; ?>

            <?php if(Auth::user()->canManageBrands()): ?>
                <li><a href="/brands">Brands</a></li>
            <?php else: ?>
                <li class="restricted"><a href="#">Brands (Admin Access Required)</a></li>
            <?php endif; ?>

            <?php if(Auth::user()->canManageBranches()): ?>
                <li><a href="/branches">Branches</a></li>
            <?php else: ?>
                <li class="restricted"><a href="#">Branches (Admin Access Required)</a></li>
            <?php endif; ?>

            <li><a href="/users">Users</a></li>
            <li><a href="/pos-devices">POS Devices</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>Product Management</h2>
        <ul>
            <li><a href="/categories">Categories</a></li>
            <li><a href="/divisions">Divisions</a></li>
            <li><a href="/groups">Groups</a></li>
            <li><a href="/products">Products</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>POS & Sales</h2>
        <ul>
            <li><a href="/sales">Sales</a></li>
            <li><a href="/sync-logs">Sync Logs</a></li>
            <li><a href="/pos-layouts">POS Layouts</a></li>
            <li><a href="/pos-limits">POS Limits</a></li>
        </ul>
    </div>

    <div class="section">
        <h2>Operations</h2>
        <ul>
            <li><a href="/purchase-orders">Purchase Orders</a></li>
            <li><a href="/payments">Payments</a></li>
            <li><a href="/notifications">Notifications</a></li>
        </ul>
    </div>

</body>
</html>
<?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/dashboard.blade.php ENDPATH**/ ?>