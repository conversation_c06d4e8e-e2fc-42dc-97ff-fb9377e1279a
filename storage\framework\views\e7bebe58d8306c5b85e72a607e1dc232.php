<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ERP System Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            overflow-x: hidden;
        }

        /* Sidebar Styles */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar-header h2 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 14px;
            opacity: 0.8;
        }

        .sidebar-menu {
            padding: 20px 0;
        }

        .menu-section {
            margin-bottom: 30px;
        }

        .menu-section-title {
            padding: 0 20px 10px;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 1px;
            opacity: 0.7;
            font-weight: 600;
        }

        .menu-item {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
            transform: translateX(5px);
        }

        .menu-item.active {
            background: rgba(255,255,255,0.15);
            border-left-color: #fff;
        }

        .menu-item i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
        }

        .menu-item.restricted {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .menu-item.restricted:hover {
            background: none;
            border-left-color: transparent;
            transform: none;
        }

        .user-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            border-top: 1px solid rgba(255,255,255,0.1);
            background: rgba(0,0,0,0.1);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .user-role {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 10px;
        }

        .logout-btn {
            width: 100%;
            padding: 8px;
            background: rgba(220, 53, 69, 0.8);
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            transition: background 0.3s ease;
            font-size: 12px;
        }

        .logout-btn:hover {
            background: rgba(220, 53, 69, 1);
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
            transition: margin-left 0.3s ease;
        }

        .top-bar {
            background: white;
            padding: 15px 30px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .breadcrumb {
            font-size: 14px;
            color: #666;
        }

        .content-area {
            padding: 30px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .mobile-toggle {
                display: block;
                background: none;
                border: none;
                font-size: 20px;
                cursor: pointer;
            }
        }

        .mobile-toggle {
            display: none;
        }

        /* Dashboard Cards */
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #666;
        }

        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: white;
        }

        .card-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .card-description {
            font-size: 14px;
            color: #666;
            margin-bottom: 15px;
        }

        .card-link {
            color: #007bff;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
        }

        .card-link:hover {
            text-decoration: underline;
        }

        /* Quick Actions */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .quick-action-btn {
            padding: 15px 20px;
            border: none;
            border-radius: 8px;
            color: white;
            text-decoration: none;
            text-align: center;
            font-weight: 500;
            transition: all 0.3s ease;
            display: block;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
        .btn-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
        .btn-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); }

        /* Recent Activity */
        .recent-activity {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .activity-header {
            padding: 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .activity-header h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .activity-list {
            padding: 20px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 16px;
            color: #666;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #666;
        }

        .activity-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-expired {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ERP System</h2>
            <p>Management Dashboard</p>
        </div>

        <div class="sidebar-menu">
            <?php if(Auth::user()->isMasterAdmin()): ?>
                <!-- Master Admin Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="<?php echo e(route('dashboard')); ?>" class="menu-item active">
                        <i>📊</i> Overview
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">Master Management</div>
                    <a href="<?php echo e(route('admin.companies.index')); ?>" class="menu-item">
                        <i>🏢</i> Companies
                    </a>
                    <a href="<?php echo e(route('admin.brands.index')); ?>" class="menu-item">
                        <i>🏷️</i> Brands
                    </a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="menu-item">
                        <i>👥</i> Users
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">System</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> POS Devices
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> Sales
                    </a>
                    <a href="/sync-logs" class="menu-item">
                        <i>🔄</i> Sync Logs
                    </a>
                </div>
            <?php else: ?>
                <!-- Company User Menu -->
                <div class="menu-section">
                    <div class="menu-section-title">Dashboard</div>
                    <a href="<?php echo e(route('dashboard')); ?>" class="menu-item active">
                        <i>📊</i> Overview
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">Management</div>
                    <?php if(Auth::user()->canManageBrands()): ?>
                        <a href="<?php echo e(route('admin.brands.index')); ?>" class="menu-item">
                            <i>🏷️</i> Brands
                        </a>
                    <?php else: ?>
                        <a href="#" class="menu-item restricted">
                            <i>🏷️</i> Brands (Admin Only)
                        </a>
                    <?php endif; ?>

                    <?php if(Auth::user()->canManageBranches()): ?>
                        <a href="/branches" class="menu-item">
                            <i>🏪</i> Branches
                        </a>
                    <?php else: ?>
                        <a href="#" class="menu-item restricted">
                            <i>🏪</i> Branches (Admin Only)
                        </a>
                    <?php endif; ?>

                    <a href="<?php echo e(route('admin.users.index')); ?>" class="menu-item">
                        <i>👥</i> Users
                    </a>
                </div>

                <div class="menu-section">
                    <div class="menu-section-title">Operations</div>
                    <a href="/pos-devices" class="menu-item">
                        <i>🖥️</i> POS Devices
                    </a>
                    <a href="/sales" class="menu-item">
                        <i>💰</i> Sales
                    </a>
                    <a href="/products" class="menu-item">
                        <i>📦</i> Products
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- User Info at Bottom -->
        <div class="user-info">
            <div class="user-avatar">
                <?php echo e(strtoupper(substr(Auth::user()->name, 0, 1))); ?>

            </div>
            <div class="user-name"><?php echo e(Auth::user()->name); ?></div>
            <div class="user-role">
                <?php if(Auth::user()->isMasterAdmin()): ?>
                    Master Administrator
                <?php else: ?>
                    <?php echo e(ucfirst(Auth::user()->role)); ?> - <?php echo e(Auth::user()->company->name ?? 'No Company'); ?>

                <?php endif; ?>
            </div>
            <form action="<?php echo e(route('logout')); ?>" method="POST" style="margin: 0;">
                <?php echo csrf_field(); ?>
                <button type="submit" class="logout-btn">Logout</button>
            </form>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Bar -->
        <div class="top-bar">
            <div>
                <button class="mobile-toggle" onclick="toggleSidebar()">☰</button>
                <span class="page-title">
                    <?php if(Auth::user()->isMasterAdmin()): ?>
                        Master Admin Dashboard
                    <?php else: ?>
                        Company Dashboard
                    <?php endif; ?>
                </span>
            </div>
            <div class="breadcrumb">
                Home > Dashboard
            </div>
        </div>

        <!-- Content Area -->
        <div class="content-area">
            <?php if(Auth::user()->isMasterAdmin()): ?>
                <!-- Master Admin Dashboard Content -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Companies</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">🏢</div>
                        </div>
                        <div class="card-value"><?php echo e(\App\Models\Company::count()); ?></div>
                        <div class="card-description">Total Licensed Companies</div>
                        <a href="<?php echo e(route('admin.companies.index')); ?>" class="card-link">Manage Companies →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Brands</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">🏷️</div>
                        </div>
                        <div class="card-value"><?php echo e(\App\Models\Brand::count()); ?></div>
                        <div class="card-description">Total Brands</div>
                        <a href="<?php echo e(route('admin.brands.index')); ?>" class="card-link">Manage Brands →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Users</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">👥</div>
                        </div>
                        <div class="card-value"><?php echo e(\App\Models\User::where('id', '!=', 1)->count()); ?></div>
                        <div class="card-description">Total System Users</div>
                        <a href="<?php echo e(route('admin.users.index')); ?>" class="card-link">Manage Users →</a>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">POS Devices</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">🖥️</div>
                        </div>
                        <div class="card-value"><?php echo e(\App\Models\PosDevice::count()); ?></div>
                        <div class="card-description">Active POS Devices</div>
                        <a href="#" class="card-link">Manage Devices →</a>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions">
                    <a href="<?php echo e(route('admin.companies.create')); ?>" class="quick-action-btn btn-primary">
                        + Add New Company
                    </a>
                    <a href="<?php echo e(route('admin.brands.create')); ?>" class="quick-action-btn btn-success">
                        + Add New Brand
                    </a>
                    <a href="<?php echo e(route('admin.users.create')); ?>" class="quick-action-btn btn-warning">
                        + Add New User
                    </a>
                </div>

                <!-- Recent Activity -->
                <div class="recent-activity">
                    <div class="activity-header">
                        <h3>Recent Activity</h3>
                    </div>
                    <div class="activity-list">
                        <?php
                            $recentCompanies = \App\Models\Company::latest()->take(5)->get();
                        ?>
                        <?php if($recentCompanies->count() > 0): ?>
                            <?php $__currentLoopData = $recentCompanies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $company): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="activity-item">
                                    <div class="activity-icon">🏢</div>
                                    <div class="activity-content">
                                        <div class="activity-title"><?php echo e($company->name); ?></div>
                                        <div class="activity-time">Added <?php echo e($company->created_at->diffForHumans()); ?></div>
                                    </div>
                                    <div class="activity-status <?php echo e($company->license_expiry > now() ? 'status-active' : 'status-expired'); ?>">
                                        <?php echo e($company->license_expiry > now() ? 'Active' : 'Expired'); ?>

                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <?php else: ?>
                            <div class="activity-item">
                                <div class="activity-icon">📝</div>
                                <div class="activity-content">
                                    <div class="activity-title">No recent activity</div>
                                    <div class="activity-time">Start by adding your first company</div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Company User Dashboard Content -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Your Company</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #007bff, #0056b3);">🏢</div>
                        </div>
                        <div class="card-value"><?php echo e(Auth::user()->company->name); ?></div>
                        <div class="card-description">
                            License: <?php echo e(Auth::user()->company->license_expiry > now() ? 'Active' : 'Expired'); ?>

                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Brands</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #28a745, #1e7e34);">🏷️</div>
                        </div>
                        <div class="card-value"><?php echo e(Auth::user()->company->brands->count()); ?></div>
                        <div class="card-description">Company Brands</div>
                        <?php if(Auth::user()->canManageBrands()): ?>
                            <a href="<?php echo e(route('admin.brands.index')); ?>" class="card-link">Manage Brands →</a>
                        <?php endif; ?>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">Branches</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #ffc107, #e0a800);">🏪</div>
                        </div>
                        <div class="card-value"><?php echo e(Auth::user()->company->branches->count()); ?></div>
                        <div class="card-description">Store Locations</div>
                        <?php if(Auth::user()->canManageBranches()): ?>
                            <a href="/branches" class="card-link">Manage Branches →</a>
                        <?php endif; ?>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-header">
                            <div class="card-title">POS Devices</div>
                            <div class="card-icon" style="background: linear-gradient(135deg, #dc3545, #c82333);">🖥️</div>
                        </div>
                        <div class="card-value"><?php echo e(Auth::user()->company->branches->sum(function($branch) { return $branch->posDevices->count(); })); ?></div>
                        <div class="card-description">Active Devices</div>
                        <a href="/pos-devices" class="card-link">View Devices →</a>
                    </div>
                </div>

                <!-- Company Quick Actions -->
                <div class="quick-actions">
                    <?php if(Auth::user()->canManageBrands()): ?>
                        <a href="<?php echo e(route('admin.brands.create', ['company_id' => Auth::user()->company_id])); ?>" class="quick-action-btn btn-success">
                            + Add New Brand
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo e(route('admin.users.create', ['company_id' => Auth::user()->company_id])); ?>" class="quick-action-btn btn-warning">
                        + Add New User
                    </a>
                    <a href="/products" class="quick-action-btn btn-info">
                        📦 Manage Products
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('active');
        }

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const toggle = document.querySelector('.mobile-toggle');

            if (window.innerWidth <= 768 &&
                !sidebar.contains(event.target) &&
                !toggle.contains(event.target)) {
                sidebar.classList.remove('active');
            }
        });
    </script>



</body>
</html>
<?php /**PATH C:\xampp\htdocs\erp-pos-system\resources\views/dashboard.blade.php ENDPATH**/ ?>