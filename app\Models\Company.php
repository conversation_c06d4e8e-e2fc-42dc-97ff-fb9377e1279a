<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'license_key', 'license_expiry', 'pos_limit'];

    public function brands()
    {
        return $this->hasMany(Brand::class);
    }

    public function branches()
    {
        return $this->hasMany(Branch::class);
    }

    public function users()
    {
        return $this->hasMany(User::class);
    }

    public function posLimits()
    {
        return $this->hasMany(PosLimit::class);
    }
}
