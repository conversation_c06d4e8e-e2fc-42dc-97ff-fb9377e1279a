<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Company - ERP System</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f9f9f9; padding: 20px; margin: 0; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .header h1 { margin: 0; color: #333; }
        .form-container { background: #fff; padding: 30px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px; }
        .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #007bff; }
        .form-group small { color: #666; font-size: 12px; }
        .error { color: #dc3545; font-size: 12px; margin-top: 5px; }
        .btn { padding: 12px 24px; border: none; border-radius: 5px; text-decoration: none; display: inline-block; cursor: pointer; font-size: 14px; margin-right: 10px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .alert { padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .license-info { background: #e9ecef; padding: 15px; border-radius: 5px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Add New Company</h1>
        </div>

        @if($errors->any())
            <div class="alert alert-danger">
                <strong>Please fix the following errors:</strong>
                <ul style="margin: 10px 0 0 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif

        <div class="form-container">
            <form method="POST" action="{{ route('admin.companies.store') }}">
                @csrf
                
                <div class="form-group">
                    <label for="name">Company Name *</label>
                    <input type="text" name="name" id="name" value="{{ old('name') }}" required>
                    <small>Enter the full legal name of the company</small>
                    @error('name')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="license_key">License Key *</label>
                        <input type="text" name="license_key" id="license_key" value="{{ old('license_key', 'LIC-' . strtoupper(Str::random(8))) }}" required>
                        <small>Unique license identifier for this company</small>
                        @error('license_key')
                            <div class="error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="pos_limit">POS Device Limit *</label>
                        <input type="number" name="pos_limit" id="pos_limit" value="{{ old('pos_limit', 5) }}" min="1" max="100" required>
                        <small>Maximum number of POS devices allowed</small>
                        @error('pos_limit')
                            <div class="error">{{ $message }}</div>
                        @enderror
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="license_expiry">License Expiry Date *</label>
                        <input type="date" name="license_expiry" id="license_expiry" value="{{ old('license_expiry', now()->addYear()->format('Y-m-d')) }}" required>
                        <small>When the company's license expires</small>
                        @error('license_expiry')
                            <div class="error">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group">
                        <label for="status">Initial Status</label>
                        <select name="status" id="status">
                            <option value="active" {{ old('status', 'active') == 'active' ? 'selected' : '' }}>Active</option>
                            <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                            <option value="suspended" {{ old('status') == 'suspended' ? 'selected' : '' }}>Suspended</option>
                        </select>
                        <small>Current status of the company license</small>
                    </div>
                </div>

                <div class="form-group">
                    <label for="contact_email">Contact Email</label>
                    <input type="email" name="contact_email" id="contact_email" value="{{ old('contact_email') }}">
                    <small>Primary contact email for this company</small>
                    @error('contact_email')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="contact_phone">Contact Phone</label>
                    <input type="tel" name="contact_phone" id="contact_phone" value="{{ old('contact_phone') }}">
                    <small>Primary contact phone number</small>
                    @error('contact_phone')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="address">Address</label>
                    <textarea name="address" id="address" rows="3">{{ old('address') }}</textarea>
                    <small>Company's business address</small>
                    @error('address')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="form-group">
                    <label for="notes">Notes</label>
                    <textarea name="notes" id="notes" rows="3">{{ old('notes') }}</textarea>
                    <small>Any additional notes about this company</small>
                    @error('notes')
                        <div class="error">{{ $message }}</div>
                    @enderror
                </div>

                <div class="license-info">
                    <h4 style="margin: 0 0 10px 0;">License Information</h4>
                    <p style="margin: 0; font-size: 14px; color: #666;">
                        This company will be able to create up to <strong id="pos-limit-display">5</strong> POS devices 
                        and the license will be valid until <strong id="expiry-display">{{ now()->addYear()->format('M d, Y') }}</strong>.
                    </p>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button type="submit" class="btn btn-primary">Create Company</button>
                    <a href="{{ route('admin.companies.index') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Update license info display
        document.getElementById('pos_limit').addEventListener('input', function() {
            document.getElementById('pos-limit-display').textContent = this.value;
        });

        document.getElementById('license_expiry').addEventListener('input', function() {
            const date = new Date(this.value);
            document.getElementById('expiry-display').textContent = date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        });

        // Generate random license key
        document.getElementById('name').addEventListener('input', function() {
            if (this.value) {
                const prefix = this.value.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '');
                const random = Math.random().toString(36).substring(2, 8).toUpperCase();
                document.getElementById('license_key').value = `${prefix}-${random}`;
            }
        });
    </script>
</body>
</html>
